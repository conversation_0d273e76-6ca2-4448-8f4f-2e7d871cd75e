package com.gnico.majo.di

import com.gnico.majo.adapter.controller.rest.ProductoController
import com.gnico.majo.adapter.controller.rest.SaleController
import com.gnico.majo.application.port.`in`.ProductoService
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.ProductoRepositoryPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.application.port.out.UsuarioRepository
import com.gnico.majo.application.usecase.ProductoServiceImpl
import com.gnico.majo.application.usecase.SaleServiceImpl
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.context.stopKoin
import org.koin.core.context.startKoin
import org.koin.dsl.module
import org.koin.test.KoinTest
import org.koin.test.get
import org.mockito.Mockito.mock
import kotlin.test.assertNotNull

class KoinModuleTest : KoinTest {

    @BeforeEach
    fun setup() {
        // Create a test module with mocked dependencies to avoid database connections
        val testModule = module {
            // Mock repositories
            single<SaleRepositoryPort> { mock(SaleRepositoryPort::class.java) }
            single<UsuarioRepository> { mock(UsuarioRepository::class.java) }
            single<ClienteRepository> { mock(ClienteRepository::class.java) }
            single<ProductoRepositoryPort> { mock(ProductoRepositoryPort::class.java) }
            single<SalesReportPort> { mock(SalesReportPort::class.java) }
            single<PrinterPort> { mock(PrinterPort::class.java) }

            // Mock services using the real implementations but with mocked dependencies
            single<ProductoService> { ProductoServiceImpl(get()) }
            single<SaleService> {
                SaleServiceImpl(
                    saleRepository = get(),
                    salesReport = get(),
                    usuarioRepository = get(),
                    clienteRepository = get(),
                    externalSaleRepository = mock(),
                    comprobanteService = mock(),
                    printService = mock()
                )
            }

            // Controllers
            single { SaleController(saleService = get()) }
            single { ProductoController(productoService = get()) }
        }

        startKoin {
            modules(testModule)
        }
    }

    @AfterEach
    fun tearDown() {
        stopKoin()
    }

    @Test
    fun `verify all dependencies are created and not null`() {
        // Check repositories (all mocked, should work)
        assertNotNull(get<SaleRepositoryPort>(), "SaleRepositoryPort should not be null")
        assertNotNull(get<UsuarioRepository>(), "UsuarioRepository should not be null")
        assertNotNull(get<ClienteRepository>(), "ClienteRepository should not be null")
        assertNotNull(get<SalesReportPort>(), "SalesReportPort should not be null")
        assertNotNull(get<ProductoRepositoryPort>(), "ProductoRepositoryPort should not be null")

        // Check services
        assertNotNull(get<PrinterPort>(), "PrinterPort should not be null")
        assertNotNull(get<SaleService>(), "SaleService should not be null")
        assertNotNull(get<ProductoService>(), "ProductoService should not be null")

        // Check controllers
        assertNotNull(get<SaleController>(), "SaleController should not be null")
        assertNotNull(get<ProductoController>(), "ProductoController should not be null")
    }
}
