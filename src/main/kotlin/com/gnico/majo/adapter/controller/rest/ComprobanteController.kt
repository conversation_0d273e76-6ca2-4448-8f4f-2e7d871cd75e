package com.gnico.majo.adapter.controller.rest

import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.adapter.controller.dto.ComprobanteInfoResponse
import com.gnico.majo.adapter.controller.dto.ComprobanteAttemptInfoResponse
import com.gnico.majo.adapter.controller.dto.VentaSinComprobanteInfoResponse
import com.gnico.majo.adapter.controller.dto.toResponse
import com.gnico.majo.application.domain.model.Id
import kotlinx.serialization.Serializable

class ComprobanteController(
    private val comprobanteService: ComprobanteService
) {
    
    /**
     * Genera un comprobante fiscal online (CAE) para una venta existente
     */
    suspend fun generarComprobanteOnline(request: GenerarComprobanteRequest): ComprobanteResponse {
        val comprobanteId = comprobanteService.generarComprobanteOnline(
            ventaId = Id(request.ventaId),
            tipoComprobante = request.tipoComprobante,
            puntoVenta = request.puntoVenta
        )

        return ComprobanteResponse(
            success = true,
            message = "Comprobante online generado exitosamente",
            comprobanteId = comprobanteId.value
        )
    }
    
    /**
     * Genera un comprobante fiscal offline (CAEA) para una venta existente
     */
    suspend fun generarComprobanteOffline(request: GenerarComprobanteRequest): ComprobanteResponse {
        val comprobanteId = comprobanteService.generarComprobanteOffline(
            ventaId = Id(request.ventaId),
            tipoComprobante = request.tipoComprobante,
            puntoVenta = request.puntoVenta
        )
        
        return ComprobanteResponse(
            success = true,
            message = "Comprobante offline generado exitosamente",
            comprobanteId = comprobanteId.value
        )
    }
    
    /**
     * Busca comprobantes por ID de venta
     */
    fun buscarComprobantesPorVenta(ventaId: Int): List<ComprobanteInfoResponse> {
        return comprobanteService.buscarComprobantesPorVenta(Id(ventaId))
            .map { it.toResponse() }
    }

    /**
     * Busca un comprobante por su número
     */
    fun buscarComprobantePorNumero(puntoVenta: Int, numeroComprobante: Int): ComprobanteInfoResponse? {
        return comprobanteService.buscarComprobantePorNumero(puntoVenta, numeroComprobante)
            ?.toResponse()
    }
    
    /**
     * Obtiene estadísticas de ventas sin comprobante
     */
    fun obtenerEstadisticasVentasSinComprobante(): EstadisticasVentasSinComprobanteResponse {
        val count = comprobanteService.contarVentasSinComprobante()
        return EstadisticasVentasSinComprobanteResponse(
            totalVentasSinComprobante = count
        )
    }
    
    /**
     * Obtiene lista de ventas sin comprobante para procesamiento
     */
    fun obtenerVentasSinComprobante(limit: Int = 100): List<VentaSinComprobanteInfoResponse> {
        return comprobanteService.obtenerVentasSinComprobante(limit)
            .map { it.toResponse() }
    }

    /**
     * Obtiene todos los intentos de comprobantes para una venta
     */
    fun obtenerIntentosComprobante(ventaId: Int): List<ComprobanteAttemptInfoResponse> {
        return comprobanteService.obtenerIntentosComprobante(Id(ventaId))
            .map { it.toResponse() }
    }

    /**
     * Obtiene solo los intentos fallidos de comprobantes para una venta
     */
    fun obtenerIntentosFallidos(ventaId: Int): List<ComprobanteAttemptInfoResponse> {
        return comprobanteService.obtenerIntentosFallidos(Id(ventaId))
            .map { it.toResponse() }
    }
}

@Serializable
data class GenerarComprobanteRequest(
    val ventaId: Int,
    val tipoComprobante: String? = null,  // Opcional, default: FACTURA_B
    val puntoVenta: Int? = null           // Opcional, default: desde .env
)

@Serializable
data class ComprobanteResponse(
    val success: Boolean,
    val message: String,
    val comprobanteId: Int? = null,
    val error: String? = null
)

@Serializable
data class EstadisticasVentasSinComprobanteResponse(
    val totalVentasSinComprobante: Int
)
