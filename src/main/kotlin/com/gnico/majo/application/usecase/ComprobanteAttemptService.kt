package com.gnico.majo.application.usecase

import com.gnico.majo.application.domain.model.AfipResponse
import com.gnico.majo.application.domain.model.ComprobanteAttempt
import com.gnico.majo.application.domain.model.TipoOperacionAfip
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.SaleRepositoryPort
import kotlin.system.measureTimeMillis

/**
 * Servicio dedicado para manejar el registro de intentos de comprobantes
 * Centraliza la lógica de auditoría y evita duplicación de código
 */
class ComprobanteAttemptService(
    private val saleRepository: SaleRepositoryPort
) {
    
    /**
     * Ejecuta una operación de AFIP y registra automáticamente el intento
     * @param ventaId ID de la venta
     * @param tipoComprobante Tipo de comprobante (FACTURA_B, NOTA_CREDITO_B, etc.)
     * @param puntoVenta Punto de venta
     * @param tipoOperacion Tipo de operación (CAE_ONLINE, CAEA_OFFLINE)
     * @param operation Función que ejecuta la operación de AFIP
     * @return Resultado de la operación con el intento registrado
     */
    suspend fun <T> executeWithAttemptTracking(
        ventaId: Id,
        tipoComprobante: String,
        puntoVenta: Int,
        tipoOperacion: TipoOperacionAfip,
        operation: suspend () -> AfipResponse
    ): ComprobanteOperationResult<T> {
        
        var tiempoRespuesta: Long = 0
        val afipResponse: AfipResponse = try {
            var response: AfipResponse? = null
            tiempoRespuesta = measureTimeMillis {
                response = operation()
            }
            response!!
        } catch (e: Exception) {
            // Registrar intento fallido por error de sistema
            val attemptFallido = ComprobanteAttempt.createFallidoSistema(
                ventaId = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                tipoOperacion = tipoOperacion,
                codigoError = "SYSTEM_ERROR",
                mensajeError = "Error de sistema: ${e.message}",
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
            val attemptId = saleRepository.saveComprobanteAttempt(attemptFallido)
            
            return ComprobanteOperationResult.SystemError(
                attemptId = attemptId,
                exception = e,
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
        }
        
        return if (afipResponse.isApproved()) {
            // Registrar intento exitoso
            val attemptExitoso = ComprobanteAttempt.createExitoso(
                ventaId = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                afipResponse = afipResponse,
                tiempoRespuestaMs = tiempoRespuesta.toInt(),
                comprobanteId = null // Se actualizará después si es necesario
            )
            val attemptId = saleRepository.saveComprobanteAttempt(attemptExitoso)
            
            ComprobanteOperationResult.Success(
                attemptId = attemptId,
                afipResponse = afipResponse,
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
        } else {
            // Registrar intento fallido por rechazo de AFIP
            val attemptFallido = ComprobanteAttempt.createFallidoAfip(
                ventaId = ventaId,
                tipoComprobante = tipoComprobante,
                puntoVenta = puntoVenta,
                afipResponse = afipResponse,
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
            val attemptId = saleRepository.saveComprobanteAttempt(attemptFallido)
            
            ComprobanteOperationResult.AfipRejected(
                attemptId = attemptId,
                afipResponse = afipResponse,
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
        }
    }
    
    /**
     * Actualiza un intento exitoso con el ID del comprobante generado
     */
    fun updateAttemptWithComprobante(attemptId: Id, comprobanteId: Id): Boolean {
        return saleRepository.updateComprobanteAttemptWithResult(attemptId, comprobanteId)
    }
    
    /**
     * Obtiene todos los intentos para una venta
     */
    fun getAttemptsByVenta(ventaId: Id): List<ComprobanteAttempt> {
        return saleRepository.findComprobanteAttemptsByVentaId(ventaId)
    }
    
    /**
     * Obtiene solo los intentos fallidos para una venta
     */
    fun getFailedAttemptsByVenta(ventaId: Id): List<ComprobanteAttempt> {
        return saleRepository.findFailedComprobanteAttemptsByVentaId(ventaId)
    }
}

/**
 * Resultado de una operación de comprobante con seguimiento de intentos
 */
sealed class ComprobanteOperationResult<T> {
    abstract val attemptId: Id
    abstract val tiempoRespuestaMs: Int
    
    data class Success<T>(
        override val attemptId: Id,
        val afipResponse: AfipResponse,
        override val tiempoRespuestaMs: Int
    ) : ComprobanteOperationResult<T>()
    
    data class AfipRejected<T>(
        override val attemptId: Id,
        val afipResponse: AfipResponse,
        override val tiempoRespuestaMs: Int
    ) : ComprobanteOperationResult<T>()
    
    data class SystemError<T>(
        override val attemptId: Id,
        val exception: Exception,
        override val tiempoRespuestaMs: Int
    ) : ComprobanteOperationResult<T>()
    
    fun isSuccess(): Boolean = this is Success
    fun isAfipRejected(): Boolean = this is AfipRejected
    fun isSystemError(): Boolean = this is SystemError
    
    fun getAfipResponse(): AfipResponse? = when (this) {
        is Success -> afipResponse
        is AfipRejected -> afipResponse
        is SystemError -> null
    }
    
    fun getErrorMessage(): String? = when (this) {
        is Success -> null
        is AfipRejected -> afipResponse.getObservacionesResumen()
        is SystemError -> "Error de sistema: ${exception.message}"
    }
}
