package com.gnico.majo.application.usecase

import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.application.port.`in`.ComprobanteInfo
import com.gnico.majo.application.port.`in`.ComprobanteAttemptInfo
import com.gnico.majo.application.port.`in`.VentaSinComprobanteInfo
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.ComprobanteAttempt
import com.gnico.majo.application.domain.model.TipoOperacionAfip
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.domain.model.AfipResponse
import com.gnico.majo.infrastructure.config.ComprobanteConfigurationService
import java.time.format.DateTimeFormatter
import java.text.DecimalFormat
import kotlin.system.measureTimeMillis

class ComprobanteServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val afipService: AfipService
) : ComprobanteService {
    
    private val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
    private val decimalFormatter = DecimalFormat("#,##0.00")
    
    override suspend fun generarComprobanteOnline(
        ventaId: Id,
        tipoComprobante: String?,
        puntoVenta: Int?
    ): Id {
        // Resolver configuración con valores por defecto
        val config = ComprobanteConfigurationService.resolveConfig(
            puntoVenta = puntoVenta,
            tipoComprobante = tipoComprobante
        )

        // Validar que la venta existe
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        // Validar que la venta no tiene comprobante ya emitido
        if (sale.comprobanteEmitido) {
            throw IllegalArgumentException("La venta ${sale.numeroVenta} ya tiene un comprobante emitido")
        }

        // Registrar intento y medir tiempo de respuesta
        var tiempoRespuesta: Long = 0
        val afipResponse: AfipResponse = try {
            var response: AfipResponse? = null
            tiempoRespuesta = measureTimeMillis {
                response = afipService.solicitarCAE(
                    sale = sale,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = config.puntoVenta
                )
            }
            response!!
        } catch (e: Exception) {
            // Registrar intento fallido por error de sistema
            val attemptFallido = ComprobanteAttempt.createFallidoSistema(
                ventaId = ventaId,
                tipoComprobante = config.tipoComprobante,
                puntoVenta = config.puntoVenta,
                tipoOperacion = TipoOperacionAfip.CAE_ONLINE,
                codigoError = "SYSTEM_ERROR",
                mensajeError = "Error de sistema: ${e.message}",
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
            saleRepository.saveComprobanteAttempt(attemptFallido)
            throw e
        }

        // Verificar que la respuesta sea exitosa
        if (!afipResponse.isApproved()) {
            // Registrar intento fallido por rechazo de AFIP
            val attemptFallido = ComprobanteAttempt.createFallidoAfip(
                ventaId = ventaId,
                tipoComprobante = config.tipoComprobante,
                puntoVenta = config.puntoVenta,
                afipResponse = afipResponse,
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
            saleRepository.saveComprobanteAttempt(attemptFallido)
            throw IllegalStateException("Error al generar comprobante online: ${afipResponse.getObservacionesResumen()}")
        }

        // Crear y guardar el comprobante
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta,
            ventaId = ventaId
        ).withCAE(
            cae = afipResponse.cae,
            estado = afipResponse.getEstadoDescriptivo()
        )

        val comprobanteId = saleRepository.saveComprobante(comprobante)

        // Registrar intento exitoso
        val attemptExitoso = ComprobanteAttempt.createExitoso(
            ventaId = ventaId,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta,
            afipResponse = afipResponse,
            tiempoRespuestaMs = tiempoRespuesta.toInt(),
            comprobanteId = comprobanteId
        )
        saleRepository.saveComprobanteAttempt(attemptExitoso)

        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(ventaId, true)

        return comprobanteId
    }
    
    override suspend fun generarComprobanteOffline(
        ventaId: Id,
        tipoComprobante: String?,
        puntoVenta: Int?
    ): Id {
        // Resolver configuración con valores por defecto
        val config = ComprobanteConfigurationService.resolveConfig(
            puntoVenta = puntoVenta,
            tipoComprobante = tipoComprobante
        )

        // Validar que la venta existe
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        // Validar que la venta no tiene comprobante ya emitido
        if (sale.comprobanteEmitido) {
            throw IllegalArgumentException("La venta ${sale.numeroVenta} ya tiene un comprobante emitido")
        }

        // Registrar intento y medir tiempo de respuesta
        var tiempoRespuesta: Long = 0
        val afipResponse: AfipResponse = try {
            var response: AfipResponse? = null
            tiempoRespuesta = measureTimeMillis {
                response = afipService.crearComprobanteConCAEA(
                    sale = sale,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = config.puntoVenta
                )
            }
            response!!
        } catch (e: Exception) {
            // Registrar intento fallido por error de sistema
            val attemptFallido = ComprobanteAttempt.createFallidoSistema(
                ventaId = ventaId,
                tipoComprobante = config.tipoComprobante,
                puntoVenta = config.puntoVenta,
                tipoOperacion = TipoOperacionAfip.CAEA_OFFLINE,
                codigoError = "SYSTEM_ERROR",
                mensajeError = "Error de sistema: ${e.message}",
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
            saleRepository.saveComprobanteAttempt(attemptFallido)
            throw e
        }

        // Verificar que la respuesta sea exitosa
        if (!afipResponse.isApproved()) {
            // Registrar intento fallido por rechazo de AFIP
            val attemptFallido = ComprobanteAttempt.createFallidoAfip(
                ventaId = ventaId,
                tipoComprobante = config.tipoComprobante,
                puntoVenta = config.puntoVenta,
                afipResponse = afipResponse,
                tiempoRespuestaMs = tiempoRespuesta.toInt()
            )
            saleRepository.saveComprobanteAttempt(attemptFallido)
            throw IllegalStateException("Error al generar comprobante offline: ${afipResponse.getObservacionesResumen()}")
        }

        // Crear y guardar el comprobante
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta,
            ventaId = ventaId
        ).withCAE(
            cae = afipResponse.cae,
            estado = afipResponse.getEstadoDescriptivo()
        )

        val comprobanteId = saleRepository.saveComprobante(comprobante)

        // Registrar intento exitoso
        val attemptExitoso = ComprobanteAttempt.createExitoso(
            ventaId = ventaId,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta,
            afipResponse = afipResponse,
            tiempoRespuestaMs = tiempoRespuesta.toInt(),
            comprobanteId = comprobanteId
        )
        saleRepository.saveComprobanteAttempt(attemptExitoso)

        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(ventaId, true)

        return comprobanteId
    }
    
    override fun buscarComprobantesPorVenta(ventaId: Id): List<ComprobanteInfo> {
        val comprobantes = saleRepository.findComprobantesByVentaId(ventaId)
        return comprobantes.map { comprobante ->
            ComprobanteInfo(
                id = comprobante.id ?: throw IllegalStateException("Comprobante sin ID"),
                ventaId = comprobante.venta,
                tipoComprobante = comprobante.tipoComprobante,
                puntoVenta = comprobante.puntoVenta,
                numeroComprobante = comprobante.numeroComprobante,
                cae = comprobante.cae,
                estado = comprobante.estado,
                fechaEmision = comprobante.fechaEmision.format(dateFormatter),
                montoTotal = decimalFormatter.format(comprobante.impTotal)
            )
        }
    }
    
    override fun buscarComprobantePorNumero(puntoVenta: Int, numeroComprobante: Int): ComprobanteInfo? {
        val comprobante = saleRepository.findComprobanteByNumero(puntoVenta, numeroComprobante)
            ?: return null
        
        return ComprobanteInfo(
            id = comprobante.id ?: throw IllegalStateException("Comprobante sin ID"),
            ventaId = comprobante.venta,
            tipoComprobante = comprobante.tipoComprobante,
            puntoVenta = comprobante.puntoVenta,
            numeroComprobante = comprobante.numeroComprobante,
            cae = comprobante.cae,
            estado = comprobante.estado,
            fechaEmision = comprobante.fechaEmision.format(dateFormatter),
            montoTotal = decimalFormatter.format(comprobante.impTotal)
        )
    }
    
    override fun contarVentasSinComprobante(): Int {
        return saleRepository.countSalesWithoutComprobante()
    }
    
    override fun obtenerVentasSinComprobante(limit: Int): List<VentaSinComprobanteInfo> {
        val ventas = saleRepository.findSalesWithoutComprobante(limit)
        return ventas.map { venta ->
            VentaSinComprobanteInfo(
                ventaId = venta.id ?: throw IllegalStateException("Venta sin ID"),
                numeroVenta = venta.numeroVenta,
                fechaVenta = venta.fechaVenta.format(dateFormatter),
                clienteNombre = venta.cliente?.nombre,
                usuarioNombre = venta.usuario.nombreDisplay,
                montoTotal = decimalFormatter.format(venta.montoTotal),
                medioPago = venta.medioPago
            )
        }
    }

    override fun obtenerIntentosComprobante(ventaId: Id): List<ComprobanteAttemptInfo> {
        val attempts = saleRepository.findComprobanteAttemptsByVentaId(ventaId)
        return attempts.map { attempt -> mapToComprobanteAttemptInfo(attempt) }
    }

    override fun obtenerIntentosFallidos(ventaId: Id): List<ComprobanteAttemptInfo> {
        val failedAttempts = saleRepository.findFailedComprobanteAttemptsByVentaId(ventaId)
        return failedAttempts.map { attempt -> mapToComprobanteAttemptInfo(attempt) }
    }

    private fun mapToComprobanteAttemptInfo(attempt: ComprobanteAttempt): ComprobanteAttemptInfo {
        return ComprobanteAttemptInfo(
            id = attempt.id ?: throw IllegalStateException("ComprobanteAttempt sin ID"),
            ventaId = attempt.ventaId,
            tipoComprobante = attempt.tipoComprobante,
            puntoVenta = attempt.puntoVenta,
            tipoOperacion = attempt.tipoOperacion.name,
            estado = attempt.estado.name,
            fechaIntento = attempt.fechaIntento.format(dateFormatter),
            tiempoRespuestaMs = attempt.tiempoRespuestaMs,
            cae = attempt.cae,
            numeroComprobante = attempt.numeroComprobante,
            fechaVencimientoCae = attempt.fechaVencimientoCae?.format(java.time.format.DateTimeFormatter.ofPattern("dd/MM/yyyy")),
            comprobanteId = attempt.comprobanteId,
            codigoError = attempt.codigoError,
            mensajeError = attempt.mensajeError,
            observacionesAfip = attempt.observacionesAfip
        )
    }
}
