package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.controller.dto.SaleItemRequest
import com.gnico.majo.adapter.controller.dto.SaleFilterRequest
import com.gnico.majo.adapter.controller.dto.SalePageResponse
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.ComprobanteAttempt
import com.gnico.majo.application.domain.model.TipoOperacionAfip
import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.port.out.PrinterPort
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SaleFilterCriteria
import com.gnico.majo.application.port.out.PageRequest
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.application.port.out.ExternalSaleRepositoryPort
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.application.port.out.UsuarioRepository
import com.gnico.majo.application.port.out.AfipService
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.application.domain.model.AfipResponse
import com.gnico.majo.infrastructure.config.ComprobanteConfigurationService
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.text.DecimalFormat

class SaleServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val printer: PrinterPort,
    private val salesReport: SalesReportPort,
    private val usuarioRepository: UsuarioRepository,
    private val clienteRepository: ClienteRepository,
    private val externalSaleRepository: ExternalSaleRepositoryPort,
    private val afipService: AfipService
) : SaleService {

    override suspend fun createSale(
        clienteId: Int?,
        vendedor: String,
        itemsRequest: List<SaleItemRequest>,
        medioPago: String,
        codigoTicketBalanza: String?,
        idTicketBalanza: String?,
        imprimirTicket: Boolean,
        facturaOnline: Boolean,
        facturaOffline: Boolean
    ): Id {
        // Obtener usuario por username
        val usuario = usuarioRepository.findByUsername(vendedor)
            ?: throw IllegalArgumentException("Usuario con username '$vendedor' no encontrado")

        // Obtener cliente (opcional)
        val cliente = clienteId?.let { id ->
            clienteRepository.findById(Id(id))
                ?: throw IllegalArgumentException("Cliente $id no encontrado")
        }

        // Mapear ítems usando el factory method del dominio
        // El precioUnitario ya incluye IVA
        val items = itemsRequest.map { item ->
            val tipoIva = TipoIva.fromIdOrThrow(item.tipoIvaId)

            SaleItem.create(
                productoNombre = item.productoNombre,
                cantidad = BigDecimal.valueOf(item.cantidad),
                precioUnitario = BigDecimal.valueOf(item.precioUnitario),
                tipoIva = tipoIva
            )
        }

        // Crear venta usando el factory method del dominio
        val sale = Sale.create(
            cliente = cliente,
            usuario = usuario,
            items = items,
            medioPago = medioPago,
            codigoTicketBalanza = codigoTicketBalanza,
            idTicketBalanza = idTicketBalanza
        )

        val saleId = saleRepository.saveSale(sale)
        val savedSale = saleRepository.findSaleById(saleId)
            ?: throw IllegalStateException("Venta ${saleId.value} no encontrada después de guardar")

        // Procesar facturación e impresión de forma desacoplada
        processPostSaleActions(savedSale, facturaOnline, facturaOffline, imprimirTicket)

        return saleId
    }

    /**
     * Procesa las acciones posteriores a la venta: facturación e impresión
     * Esta lógica ahora está desacoplada y puede ejecutarse independientemente
     */
    private suspend fun processPostSaleActions(
        sale: Sale,
        facturaOnline: Boolean,
        facturaOffline: Boolean,
        imprimirTicket: Boolean
    ) {
        var comprobanteCreado: Id? = null

        // Procesar facturación si se solicita
        comprobanteCreado = when {
            facturaOnline -> processOnlineInvoicing(sale)
            facturaOffline -> processOfflineInvoicing(sale)
            else -> null
        }

        // Procesar impresión si se solicita
        if (imprimirTicket) {
            processTicketPrinting(sale, comprobanteCreado)
        }
    }

    /**
     * Procesa facturación online (CAE)
     */
    private suspend fun processOnlineInvoicing(sale: Sale): Id? {
        return try {
            println("Procesando factura online para venta ${sale.numeroVenta}")

            // Usar configuración por defecto
            val config = ComprobanteConfigurationService.resolveConfig()
            val ventaId = sale.id ?: throw IllegalStateException("Sale debe tener ID")

            // Registrar intento y medir tiempo de respuesta
            var tiempoRespuesta: Long = 0
            val afipResponse = try {
                var response: AfipResponse? = null
                tiempoRespuesta = kotlin.system.measureTimeMillis {
                    response = afipService.solicitarCAE(
                        sale = sale,
                        tipoComprobante = config.tipoComprobante,
                        puntoVenta = config.puntoVenta
                    )
                }
                response!!
            } catch (e: Exception) {
                // Registrar intento fallido por error de sistema
                val attemptFallido = ComprobanteAttempt.createFallidoSistema(
                    ventaId = ventaId,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = config.puntoVenta,
                    tipoOperacion = TipoOperacionAfip.CAE_ONLINE,
                    codigoError = "SYSTEM_ERROR",
                    mensajeError = "Error de sistema: ${e.message}",
                    tiempoRespuestaMs = tiempoRespuesta.toInt()
                )
                saleRepository.saveComprobanteAttempt(attemptFallido)
                throw e
            }

            if (afipResponse.isApproved()) {
                val comprobanteId = createAndSaveComprobante(sale, afipResponse)

                // Registrar intento exitoso
                val attemptExitoso = ComprobanteAttempt.createExitoso(
                    ventaId = ventaId,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = config.puntoVenta,
                    afipResponse = afipResponse,
                    tiempoRespuestaMs = tiempoRespuesta.toInt(),
                    comprobanteId = comprobanteId
                )
                saleRepository.saveComprobanteAttempt(attemptExitoso)

                comprobanteId
            } else {
                // Registrar intento fallido por rechazo de AFIP
                val attemptFallido = ComprobanteAttempt.createFallidoAfip(
                    ventaId = ventaId,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = config.puntoVenta,
                    afipResponse = afipResponse,
                    tiempoRespuestaMs = tiempoRespuesta.toInt()
                )
                saleRepository.saveComprobanteAttempt(attemptFallido)

                println("Error en facturación online: ${afipResponse.getObservacionesResumen()}")
                null
            }
        } catch (e: Exception) {
            println("Error al procesar facturación online: ${e.message}")
            null
        }
    }

    /**
     * Procesa facturación offline (CAEA)
     */
    private suspend fun processOfflineInvoicing(sale: Sale): Id? {
        return try {
            println("Procesando factura offline para venta ${sale.numeroVenta}")

            // Usar configuración por defecto
            val config = ComprobanteConfigurationService.resolveConfig()
            val ventaId = sale.id ?: throw IllegalStateException("Sale debe tener ID")

            // Registrar intento y medir tiempo de respuesta
            var tiempoRespuesta: Long = 0
            val afipResponse = try {
                var response: AfipResponse? = null
                tiempoRespuesta = kotlin.system.measureTimeMillis {
                    response = afipService.crearComprobanteConCAEA(
                        sale = sale,
                        tipoComprobante = config.tipoComprobante,
                        puntoVenta = config.puntoVenta
                    )
                }
                response!!
            } catch (e: Exception) {
                // Registrar intento fallido por error de sistema
                val attemptFallido = ComprobanteAttempt.createFallidoSistema(
                    ventaId = ventaId,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = config.puntoVenta,
                    tipoOperacion = TipoOperacionAfip.CAEA_OFFLINE,
                    codigoError = "SYSTEM_ERROR",
                    mensajeError = "Error de sistema: ${e.message}",
                    tiempoRespuestaMs = tiempoRespuesta.toInt()
                )
                saleRepository.saveComprobanteAttempt(attemptFallido)
                throw e
            }

            if (afipResponse.isApproved()) {
                val comprobanteId = createAndSaveComprobante(sale, afipResponse)

                // Registrar intento exitoso
                val attemptExitoso = ComprobanteAttempt.createExitoso(
                    ventaId = ventaId,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = config.puntoVenta,
                    afipResponse = afipResponse,
                    tiempoRespuestaMs = tiempoRespuesta.toInt(),
                    comprobanteId = comprobanteId
                )
                saleRepository.saveComprobanteAttempt(attemptExitoso)

                comprobanteId
            } else {
                // Registrar intento fallido por rechazo de AFIP
                val attemptFallido = ComprobanteAttempt.createFallidoAfip(
                    ventaId = ventaId,
                    tipoComprobante = config.tipoComprobante,
                    puntoVenta = config.puntoVenta,
                    afipResponse = afipResponse,
                    tiempoRespuestaMs = tiempoRespuesta.toInt()
                )
                saleRepository.saveComprobanteAttempt(attemptFallido)

                println("Error en facturación offline: ${afipResponse.getObservacionesResumen()}")
                null
            }
        } catch (e: Exception) {
            println("Error al procesar facturación offline: ${e.message}")
            null
        }
    }

    /**
     * Procesa la impresión de tickets
     */
    private fun processTicketPrinting(sale: Sale, comprobanteId: Id?) {
        try {
            if (comprobanteId != null) {
                // Se creó un comprobante, imprimir ticket de factura
                val comprobante = saleRepository.findComprobanteById(comprobanteId)
                    ?: throw IllegalStateException("Comprobante ${comprobanteId.value} no encontrado")
                printer.printTicketFactura(comprobante, sale)
            } else {
                // No se creó comprobante, imprimir ticket genérico
                printer.printTicketGenerico(sale)
            }
        } catch (e: Exception) {
            println("Error al imprimir ticket: ${e.message}")
        }
    }

    /**
     * Crea y guarda un comprobante basado en la respuesta de AFIP
     */
    private fun createAndSaveComprobante(sale: Sale, afipResponse: AfipResponse): Id {
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = if (afipResponse.isOnlineOperation()) "FACTURA_B" else "FACTURA_B",
            puntoVenta = 1,
            ventaId = sale.id ?: throw IllegalStateException("Sale debe tener ID")
        ).withCAE(
            cae = afipResponse.cae,
            estado = afipResponse.getEstadoDescriptivo()
        )

        val comprobanteId = saleRepository.saveComprobante(comprobante)

        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(
            sale.id ?: throw IllegalStateException("Sale debe tener ID"),
            true
        )

        return comprobanteId
    }

    @Deprecated("Usar ComprobanteService.generarComprobanteOnline() en su lugar")
    override fun createComprobante(
        ventaId: Id,
        tipoComprobante: String,
        puntoVenta: Int
    ): Id {
        val sale = saleRepository.findSaleById(ventaId)
            ?: throw IllegalArgumentException("Venta ${ventaId.value} no encontrada")

        // Validar configuración
        val config = ComprobanteConfigurationService.resolveConfig(
            puntoVenta = puntoVenta,
            tipoComprobante = tipoComprobante
        )

        // Usar el factory method del dominio que incluye las validaciones
        val comprobante = Comprobante.createFromSale(
            sale = sale,
            tipoComprobante = config.tipoComprobante,
            puntoVenta = config.puntoVenta,
            ventaId = ventaId
        )

        val comprobanteId = saleRepository.saveComprobante(comprobante)
        val savedComprobante = saleRepository.findComprobanteById(comprobanteId)
            ?: throw IllegalStateException("Comprobante ${comprobanteId.value} no encontrado")

        // Marcar la venta como comprobante emitido
        saleRepository.updateComprobanteEmitido(ventaId, true)

        // Imprimir comprobante
        printer.printComprobante(savedComprobante, sale)

        return comprobanteId
    }


    override suspend fun getExternalSaleDetails(codigo: String): ExternalSaleDetail? {
        require(codigo.isNotBlank()) { "El código no puede estar vacío" }
        return externalSaleRepository.findSaleDetailsByDocumentNo(codigo)
    }

    // Nuevos métodos para búsqueda y consulta de ventas

    override fun findSaleById(ventaId: Id): Sale? {
        return saleRepository.findSaleById(ventaId)
    }

    override fun findSaleByNumero(numeroVenta: String): Sale? {
        require(numeroVenta.isNotBlank()) { "El número de venta no puede estar vacío" }
        return saleRepository.findSaleByNumeroVenta(numeroVenta)
    }

    override fun findSalesByDateRange(fechaDesde: String, fechaHasta: String): List<Sale> {
        require(fechaDesde.isNotBlank()) { "La fecha desde no puede estar vacía" }
        require(fechaHasta.isNotBlank()) { "La fecha hasta no puede estar vacía" }

        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val startDate = LocalDateTime.parse(fechaDesde, formatter)
        val endDate = LocalDateTime.parse(fechaHasta, formatter)

        require(startDate.isBefore(endDate) || startDate.isEqual(endDate)) {
            "La fecha desde debe ser anterior o igual a la fecha hasta"
        }

        return saleRepository.findSalesByDateRange(startDate, endDate)
    }

    override fun findSalesByUsuario(username: String): List<Sale> {
        require(username.isNotBlank()) { "El username no puede estar vacío" }
        return saleRepository.findSalesByUsuario(username)
    }

    override fun findSalesWithFilters(filterRequest: SaleFilterRequest): SalePageResponse {
        // Validar parámetros de paginación
        require(filterRequest.page >= 1) { "La página debe ser mayor o igual a 1" }
        require(filterRequest.size >= 1 && filterRequest.size <= 100) { "El tamaño de página debe estar entre 1 y 100" }

        // Parsear fechas si están presentes
        val fechaDesde = filterRequest.fechaDesde?.let { fecha ->
            require(fecha.isNotBlank()) { "La fecha desde no puede estar vacía" }
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            LocalDateTime.parse(fecha, formatter)
        }

        val fechaHasta = filterRequest.fechaHasta?.let { fecha ->
            require(fecha.isNotBlank()) { "La fecha hasta no puede estar vacía" }
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            LocalDateTime.parse(fecha, formatter)
        }

        // Validar rango de fechas
        if (fechaDesde != null && fechaHasta != null) {
            require(fechaDesde.isBefore(fechaHasta) || fechaDesde.isEqual(fechaHasta)) {
                "La fecha desde debe ser anterior o igual a la fecha hasta"
            }
        }

        // Validar usuarios si están presentes
        if (!filterRequest.usuarios.isNullOrEmpty()) {
            filterRequest.usuarios.forEach { username ->
                require(username.isNotBlank()) { "Los usernames no pueden estar vacíos" }
            }
        }

        // Validar medios de pago si están presentes
        if (!filterRequest.mediosPago.isNullOrEmpty()) {
            filterRequest.mediosPago.forEach { medioPago ->
                require(medioPago.isNotBlank()) { "Los medios de pago no pueden estar vacíos" }
            }
        }

        // Crear criterios de filtrado
        val criteria = SaleFilterCriteria(
            fechaDesde = fechaDesde,
            fechaHasta = fechaHasta,
            usuarios = filterRequest.usuarios,
            comprobanteEmitido = filterRequest.comprobanteEmitido,
            mediosPago = filterRequest.mediosPago
        )

        // Crear request de paginación
        val pageRequest = PageRequest(
            page = filterRequest.page,
            size = filterRequest.size
        )

        // Ejecutar búsqueda
        val pageResult = saleRepository.findSalesWithFilters(criteria, pageRequest)

        // Convertir a response
        return SalePageResponse(
            content = pageResult.content.map { sale -> mapToSaleResponse(sale) },
            page = pageResult.page,
            size = pageResult.size,
            totalElements = pageResult.totalElements,
            totalPages = pageResult.totalPages,
            hasNext = pageResult.hasNext,
            hasPrevious = pageResult.hasPrevious
        )
    }

    private fun mapToSaleResponse(sale: Sale): com.gnico.majo.adapter.controller.dto.SaleResponse {
        val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
        val decimalFormatter = DecimalFormat("#,##0.00")

        return com.gnico.majo.adapter.controller.dto.SaleResponse(
            id = sale.id!!.value,
            numeroVenta = sale.numeroVenta,
            fechaVenta = sale.fechaVenta.format(dateFormatter),
            clienteId = sale.cliente?.id?.value,
            clienteNombre = sale.cliente?.nombre,
            usuarioUsername = sale.usuario.username,
            usuarioNombre = sale.usuario.nombreDisplay,
            montoTotal = decimalFormatter.format(sale.montoTotal),
            medioPago = sale.medioPago,
            comprobanteEmitido = sale.comprobanteEmitido,
            codigoTicketBalanza = sale.codigoTicketBalanza,
            idTicketBalanza = sale.idTicketBalanza,
            items = sale.items.map { item ->
                com.gnico.majo.adapter.controller.dto.SaleItemResponse(
                    productoNombre = item.productoNombre,
                    cantidad = item.cantidad.toDouble(),
                    precioUnitario = decimalFormatter.format(item.precioUnitario),
                    subtotal = decimalFormatter.format(item.subtotal),
                    tipoIvaId = item.tipoIva.id
                )
            }
        )
    }
}