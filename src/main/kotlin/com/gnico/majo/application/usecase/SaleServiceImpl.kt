package com.gnico.majo.application.usecase

import com.gnico.majo.adapter.controller.dto.SaleItemRequest
import com.gnico.majo.adapter.controller.dto.SaleFilterRequest
import com.gnico.majo.adapter.controller.dto.SalePageResponse
import com.gnico.majo.application.port.`in`.SaleService
import com.gnico.majo.application.port.`in`.ComprobanteService
import com.gnico.majo.application.port.`in`.PrintService
import com.gnico.majo.application.domain.model.Comprobante
import com.gnico.majo.application.domain.model.ExternalSaleDetail
import com.gnico.majo.application.domain.model.Id
import com.gnico.majo.application.domain.model.Sale
import com.gnico.majo.application.domain.model.SaleItem
import com.gnico.majo.application.port.out.SaleRepositoryPort
import com.gnico.majo.application.port.out.SaleFilterCriteria
import com.gnico.majo.application.port.out.PageRequest
import com.gnico.majo.application.port.out.ClienteRepository
import com.gnico.majo.application.port.out.ExternalSaleRepositoryPort
import com.gnico.majo.application.domain.model.TipoIva
import com.gnico.majo.application.port.out.UsuarioRepository
import com.gnico.majo.application.port.out.SalesReportPort
import com.gnico.majo.infrastructure.config.ComprobanteConfigurationService
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.text.DecimalFormat

class SaleServiceImpl(
    private val saleRepository: SaleRepositoryPort,
    private val salesReport: SalesReportPort,
    private val usuarioRepository: UsuarioRepository,
    private val clienteRepository: ClienteRepository,
    private val externalSaleRepository: ExternalSaleRepositoryPort,
    private val comprobanteService: ComprobanteService,
    private val printService: PrintService
) : SaleService {

    override suspend fun createSale(
        clienteId: Int?,
        vendedor: String,
        itemsRequest: List<SaleItemRequest>,
        medioPago: String,
        codigoTicketBalanza: String?,
        idTicketBalanza: String?,
        imprimirTicket: Boolean,
        facturaOnline: Boolean,
        facturaOffline: Boolean
    ): Id {
        // Obtener usuario por username
        val usuario = usuarioRepository.findByUsername(vendedor)
            ?: throw IllegalArgumentException("Usuario con username '$vendedor' no encontrado")

        // Obtener cliente (opcional)
        val cliente = clienteId?.let { id ->
            clienteRepository.findById(Id(id))
                ?: throw IllegalArgumentException("Cliente $id no encontrado")
        }

        // Mapear ítems usando el factory method del dominio
        // El precioUnitario ya incluye IVA
        val items = itemsRequest.map { item ->
            val tipoIva = TipoIva.fromIdOrThrow(item.tipoIvaId)

            SaleItem.create(
                productoNombre = item.productoNombre,
                cantidad = BigDecimal.valueOf(item.cantidad),
                precioUnitario = BigDecimal.valueOf(item.precioUnitario),
                tipoIva = tipoIva
            )
        }

        // Crear venta usando el factory method del dominio
        val sale = Sale.create(
            cliente = cliente,
            usuario = usuario,
            items = items,
            medioPago = medioPago,
            codigoTicketBalanza = codigoTicketBalanza,
            idTicketBalanza = idTicketBalanza
        )

        val saleId = saleRepository.saveSale(sale)
        val savedSale = saleRepository.findSaleById(saleId)
            ?: throw IllegalStateException("Venta ${saleId.value} no encontrada después de guardar")

        // Procesar facturación e impresión de forma desacoplada
        processPostSaleActions(savedSale, facturaOnline, facturaOffline, imprimirTicket)

        return saleId
    }

    /**
     * Procesa las acciones posteriores a la venta: facturación e impresión
     * Esta lógica ahora está desacoplada y puede ejecutarse independientemente
     * Delega hacia los servicios especializados para evitar duplicación de código
     */
    private suspend fun processPostSaleActions(
        sale: Sale,
        facturaOnline: Boolean,
        facturaOffline: Boolean,
        imprimirTicket: Boolean
    ) {
        val ventaId = sale.id ?: throw IllegalStateException("Sale debe tener ID")
        var comprobanteCreado: Id? = null

        // Procesar facturación si se solicita - delegando a ComprobanteService
        comprobanteCreado = when {
            facturaOnline -> processOnlineInvoicing(ventaId)
            facturaOffline -> processOfflineInvoicing(ventaId)
            else -> null
        }

        // Procesar impresión si se solicita - delegando a PrintService
        if (imprimirTicket) {
            processTicketPrinting(ventaId, comprobanteCreado)
        }
    }

    /**
     * Procesa facturación online (CAE) - delegando a ComprobanteService
     */
    private suspend fun processOnlineInvoicing(ventaId: Id): Id? {
        return try {
            println("Procesando factura online para venta ${ventaId.value}")
            comprobanteService.generarComprobanteOnline(ventaId)
        } catch (e: Exception) {
            println("Error al procesar facturación online: ${e.message}")
            null
        }
    }

    /**
     * Procesa facturación offline (CAEA) - delegando a ComprobanteService
     */
    private suspend fun processOfflineInvoicing(ventaId: Id): Id? {
        return try {
            println("Procesando factura offline para venta ${ventaId.value}")
            comprobanteService.generarComprobanteOffline(ventaId)
        } catch (e: Exception) {
            println("Error al procesar facturación offline: ${e.message}")
            null
        }
    }

    /**
     * Procesa la impresión de tickets - delegando a PrintService
     */
    private fun processTicketPrinting(ventaId: Id, comprobanteId: Id?) {
        try {
            if (comprobanteId != null) {
                // Se creó un comprobante, imprimir ticket de factura
                printService.imprimirTicketComprobante(comprobanteId)
            } else {
                // No se creó comprobante, imprimir ticket genérico
                printService.imprimirTicketVenta(ventaId)
            }
        } catch (e: Exception) {
            println("Error al imprimir ticket: ${e.message}")
        }
    }






    override suspend fun getExternalSaleDetails(codigo: String): ExternalSaleDetail? {
        require(codigo.isNotBlank()) { "El código no puede estar vacío" }
        return externalSaleRepository.findSaleDetailsByDocumentNo(codigo)
    }

    // Nuevos métodos para búsqueda y consulta de ventas

    override fun findSaleById(ventaId: Id): Sale? {
        return saleRepository.findSaleById(ventaId)
    }

    override fun findSaleByNumero(numeroVenta: String): Sale? {
        require(numeroVenta.isNotBlank()) { "El número de venta no puede estar vacío" }
        return saleRepository.findSaleByNumeroVenta(numeroVenta)
    }

    override fun findSalesByDateRange(fechaDesde: String, fechaHasta: String): List<Sale> {
        require(fechaDesde.isNotBlank()) { "La fecha desde no puede estar vacía" }
        require(fechaHasta.isNotBlank()) { "La fecha hasta no puede estar vacía" }

        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        val startDate = LocalDateTime.parse(fechaDesde, formatter)
        val endDate = LocalDateTime.parse(fechaHasta, formatter)

        require(startDate.isBefore(endDate) || startDate.isEqual(endDate)) {
            "La fecha desde debe ser anterior o igual a la fecha hasta"
        }

        return saleRepository.findSalesByDateRange(startDate, endDate)
    }

    override fun findSalesByUsuario(username: String): List<Sale> {
        require(username.isNotBlank()) { "El username no puede estar vacío" }
        return saleRepository.findSalesByUsuario(username)
    }

    override fun findSalesWithFilters(filterRequest: SaleFilterRequest): SalePageResponse {
        // Validar parámetros de paginación
        require(filterRequest.page >= 1) { "La página debe ser mayor o igual a 1" }
        require(filterRequest.size >= 1 && filterRequest.size <= 100) { "El tamaño de página debe estar entre 1 y 100" }

        // Parsear fechas si están presentes
        val fechaDesde = filterRequest.fechaDesde?.let { fecha ->
            require(fecha.isNotBlank()) { "La fecha desde no puede estar vacía" }
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            LocalDateTime.parse(fecha, formatter)
        }

        val fechaHasta = filterRequest.fechaHasta?.let { fecha ->
            require(fecha.isNotBlank()) { "La fecha hasta no puede estar vacía" }
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            LocalDateTime.parse(fecha, formatter)
        }

        // Validar rango de fechas
        if (fechaDesde != null && fechaHasta != null) {
            require(fechaDesde.isBefore(fechaHasta) || fechaDesde.isEqual(fechaHasta)) {
                "La fecha desde debe ser anterior o igual a la fecha hasta"
            }
        }

        // Validar usuarios si están presentes
        if (!filterRequest.usuarios.isNullOrEmpty()) {
            filterRequest.usuarios.forEach { username ->
                require(username.isNotBlank()) { "Los usernames no pueden estar vacíos" }
            }
        }

        // Validar medios de pago si están presentes
        if (!filterRequest.mediosPago.isNullOrEmpty()) {
            filterRequest.mediosPago.forEach { medioPago ->
                require(medioPago.isNotBlank()) { "Los medios de pago no pueden estar vacíos" }
            }
        }

        // Crear criterios de filtrado
        val criteria = SaleFilterCriteria(
            fechaDesde = fechaDesde,
            fechaHasta = fechaHasta,
            usuarios = filterRequest.usuarios,
            comprobanteEmitido = filterRequest.comprobanteEmitido,
            mediosPago = filterRequest.mediosPago
        )

        // Crear request de paginación
        val pageRequest = PageRequest(
            page = filterRequest.page,
            size = filterRequest.size
        )

        // Ejecutar búsqueda
        val pageResult = saleRepository.findSalesWithFilters(criteria, pageRequest)

        // Convertir a response
        return SalePageResponse(
            content = pageResult.content.map { sale -> mapToSaleResponse(sale) },
            page = pageResult.page,
            size = pageResult.size,
            totalElements = pageResult.totalElements,
            totalPages = pageResult.totalPages,
            hasNext = pageResult.hasNext,
            hasPrevious = pageResult.hasPrevious
        )
    }

    private fun mapToSaleResponse(sale: Sale): com.gnico.majo.adapter.controller.dto.SaleResponse {
        val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
        val decimalFormatter = DecimalFormat("#,##0.00")

        return com.gnico.majo.adapter.controller.dto.SaleResponse(
            id = sale.id!!.value,
            numeroVenta = sale.numeroVenta,
            fechaVenta = sale.fechaVenta.format(dateFormatter),
            clienteId = sale.cliente?.id?.value,
            clienteNombre = sale.cliente?.nombre,
            usuarioUsername = sale.usuario.username,
            usuarioNombre = sale.usuario.nombreDisplay,
            montoTotal = decimalFormatter.format(sale.montoTotal),
            medioPago = sale.medioPago,
            comprobanteEmitido = sale.comprobanteEmitido,
            codigoTicketBalanza = sale.codigoTicketBalanza,
            idTicketBalanza = sale.idTicketBalanza,
            items = sale.items.map { item ->
                com.gnico.majo.adapter.controller.dto.SaleItemResponse(
                    productoNombre = item.productoNombre,
                    cantidad = item.cantidad.toDouble(),
                    precioUnitario = decimalFormatter.format(item.precioUnitario),
                    subtotal = decimalFormatter.format(item.subtotal),
                    tipoIvaId = item.tipoIva.id
                )
            }
        )
    }
}