package com.gnico.majo.infrastructure.routes

import com.gnico.majo.adapter.controller.rest.ComprobanteController
import com.gnico.majo.adapter.controller.rest.GenerarComprobanteRequest
import com.gnico.majo.adapter.controller.dto.ErrorResponse
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

fun Application.configureComprobanteRoutes(comprobanteController: ComprobanteController) {
    routing {
        route("/api/comprobantes") {

            // POST /api/comprobantes/online - Generar comprobante online (CAE)
            post("/online") {
                try {
                    println("🔍 POST /api/comprobantes/online - Recibiendo petición")
                    val request = call.receive<GenerarComprobanteRequest>()
                    println("🔍 Request: ventaId=${request.ventaId}, tipo=${request.tipoComprobante}, pv=${request.puntoVenta}")
                    
                    val response = comprobanteController.generarComprobanteOnline(request)
                    println("✅ Comprobante online generado: ID=${response.comprobanteId}")
                    call.respond(HttpStatusCode.Created, response)
                    
                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento: ${e.message}")
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: IllegalStateException) {
                    println("❌ Error de estado: ${e.message}")
                    call.respond(HttpStatusCode.Conflict, ErrorResponse(e.message ?: "Conflict"))
                } catch (e: Exception) {
                    println("❌ Error interno: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // POST /api/comprobantes/offline - Generar comprobante offline (CAEA)
            post("/offline") {
                try {
                    println("🔍 POST /api/comprobantes/offline - Recibiendo petición")
                    val request = call.receive<GenerarComprobanteRequest>()
                    println("🔍 Request: ventaId=${request.ventaId}, tipo=${request.tipoComprobante}, pv=${request.puntoVenta}")
                    
                    val response = comprobanteController.generarComprobanteOffline(request)
                    println("✅ Comprobante offline generado: ID=${response.comprobanteId}")
                    call.respond(HttpStatusCode.Created, response)
                    
                } catch (e: IllegalArgumentException) {
                    println("❌ Error de argumento: ${e.message}")
                    call.respond(HttpStatusCode.BadRequest, ErrorResponse(e.message ?: "Invalid request"))
                } catch (e: IllegalStateException) {
                    println("❌ Error de estado: ${e.message}")
                    call.respond(HttpStatusCode.Conflict, ErrorResponse(e.message ?: "Conflict"))
                } catch (e: Exception) {
                    println("❌ Error interno: ${e.message}")
                    e.printStackTrace()
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/comprobantes/venta/{ventaId} - Buscar comprobantes por venta
            get("/venta/{ventaId}") {
                try {
                    val ventaIdParam = call.parameters["ventaId"]
                    if (ventaIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId es requerido"))
                        return@get
                    }

                    val ventaId = ventaIdParam.toIntOrNull()
                    if (ventaId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId debe ser un número válido"))
                        return@get
                    }

                    val comprobantes = comprobanteController.buscarComprobantesPorVenta(ventaId)
                    call.respond(HttpStatusCode.OK, comprobantes)
                    
                } catch (e: Exception) {
                    println("❌ Error al buscar comprobantes: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/comprobantes/{puntoVenta}/{numeroComprobante} - Buscar comprobante por número
            get("/{puntoVenta}/{numeroComprobante}") {
                try {
                    val puntoVentaParam = call.parameters["puntoVenta"]
                    val numeroComprobanteParam = call.parameters["numeroComprobante"]
                    
                    if (puntoVentaParam.isNullOrBlank() || numeroComprobanteParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("puntoVenta y numeroComprobante son requeridos"))
                        return@get
                    }

                    val puntoVenta = puntoVentaParam.toIntOrNull()
                    val numeroComprobante = numeroComprobanteParam.toIntOrNull()
                    
                    if (puntoVenta == null || numeroComprobante == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("puntoVenta y numeroComprobante deben ser números válidos"))
                        return@get
                    }

                    val comprobante = comprobanteController.buscarComprobantePorNumero(puntoVenta, numeroComprobante)
                    if (comprobante != null) {
                        call.respond(HttpStatusCode.OK, comprobante)
                    } else {
                        call.respond(HttpStatusCode.NotFound, ErrorResponse("Comprobante no encontrado"))
                    }
                    
                } catch (e: Exception) {
                    println("❌ Error al buscar comprobante: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/comprobantes/estadisticas/ventas-sin-comprobante - Estadísticas
            get("/estadisticas/ventas-sin-comprobante") {
                try {
                    val estadisticas = comprobanteController.obtenerEstadisticasVentasSinComprobante()
                    call.respond(HttpStatusCode.OK, estadisticas)
                    
                } catch (e: Exception) {
                    println("❌ Error al obtener estadísticas: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/comprobantes/ventas-sin-comprobante - Lista de ventas sin comprobante
            get("/ventas-sin-comprobante") {
                try {
                    val limitParam = call.request.queryParameters["limit"]
                    val limit = limitParam?.toIntOrNull() ?: 100

                    if (limit <= 0 || limit > 1000) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("limit debe estar entre 1 y 1000"))
                        return@get
                    }

                    val ventas = comprobanteController.obtenerVentasSinComprobante(limit)
                    call.respond(HttpStatusCode.OK, ventas)

                } catch (e: Exception) {
                    println("❌ Error al obtener ventas sin comprobante: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/comprobantes/intentos/{ventaId} - Obtener todos los intentos de comprobantes para una venta
            get("/intentos/{ventaId}") {
                try {
                    val ventaIdParam = call.parameters["ventaId"]
                    if (ventaIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId es requerido"))
                        return@get
                    }

                    val ventaId = ventaIdParam.toIntOrNull()
                    if (ventaId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId debe ser un número válido"))
                        return@get
                    }

                    val intentos = comprobanteController.obtenerIntentosComprobante(ventaId)
                    call.respond(HttpStatusCode.OK, intentos)

                } catch (e: Exception) {
                    println("❌ Error al obtener intentos de comprobante: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }

            // GET /api/comprobantes/intentos-fallidos/{ventaId} - Obtener solo los intentos fallidos para una venta
            get("/intentos-fallidos/{ventaId}") {
                try {
                    val ventaIdParam = call.parameters["ventaId"]
                    if (ventaIdParam.isNullOrBlank()) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId es requerido"))
                        return@get
                    }

                    val ventaId = ventaIdParam.toIntOrNull()
                    if (ventaId == null) {
                        call.respond(HttpStatusCode.BadRequest, ErrorResponse("ventaId debe ser un número válido"))
                        return@get
                    }

                    val intentosFallidos = comprobanteController.obtenerIntentosFallidos(ventaId)
                    call.respond(HttpStatusCode.OK, intentosFallidos)

                } catch (e: Exception) {
                    println("❌ Error al obtener intentos fallidos: ${e.message}")
                    call.respond(HttpStatusCode.InternalServerError, ErrorResponse("Internal server error"))
                }
            }
        }
    }
}
