-- Migración para crear tabla de intentos de comprobantes
-- Esta tabla almacena todos los intentos de generación de comprobantes, exitosos y fallidos

CREATE TABLE comprobante_attempts (
    id SERIAL PRIMARY KEY,
    venta_id INTEGER NOT NULL REFERENCES ventas(id),
    tipo_comprobante VARCHAR(20) NOT NULL, -- FACTURA_B, NOTA_CREDITO_B, etc.
    punto_venta INTEGER NOT NULL,
    tipo_operacion VARCHAR(20) NOT NULL, -- CAE_ONLINE, CAEA_OFFLINE
    estado VARCHAR(20) NOT NULL, -- EXITOSO, FALLIDO, PENDIENTE
    
    -- Datos de la respuesta de AFIP (si es exitosa)
    cae VARCHAR(50),
    numero_comprobante BIGINT,
    fecha_vencimiento_cae DATE,
    
    -- Datos de error (si falla)
    codigo_error VARCHAR(50),
    mensaje_error TEXT,
    observaciones_afip TEXT, -- JSON array de observaciones de AFIP
    
    -- Metadatos
    fecha_intento TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    tiempo_respuesta_ms INTEGER, -- Tiempo que tardó la respuesta de AFIP
    
    -- Relación con comprobante exitoso (si aplica)
    comprobante_id INTEGER REFERENCES comprobantes(id),
    
    creado_en TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Índices para mejorar rendimiento
CREATE INDEX idx_comprobante_attempts_venta_id ON comprobante_attempts(venta_id);
CREATE INDEX idx_comprobante_attempts_estado ON comprobante_attempts(estado);
CREATE INDEX idx_comprobante_attempts_fecha_intento ON comprobante_attempts(fecha_intento);
CREATE INDEX idx_comprobante_attempts_tipo_operacion ON comprobante_attempts(tipo_operacion);
CREATE INDEX idx_comprobante_attempts_comprobante_id ON comprobante_attempts(comprobante_id) WHERE comprobante_id IS NOT NULL;

-- Comentarios para documentación
COMMENT ON TABLE comprobante_attempts IS 'Registro de todos los intentos de generación de comprobantes fiscales, exitosos y fallidos';
COMMENT ON COLUMN comprobante_attempts.tipo_operacion IS 'CAE_ONLINE para solicitudes online, CAEA_OFFLINE para comprobantes offline';
COMMENT ON COLUMN comprobante_attempts.estado IS 'EXITOSO: comprobante generado correctamente, FALLIDO: error en la generación, PENDIENTE: en proceso';
COMMENT ON COLUMN comprobante_attempts.observaciones_afip IS 'Observaciones devueltas por AFIP en formato JSON';
COMMENT ON COLUMN comprobante_attempts.tiempo_respuesta_ms IS 'Tiempo de respuesta del webservice de AFIP en milisegundos';
